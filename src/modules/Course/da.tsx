import {DataController} from '../../base/baseController';
import {StatusOrder, StorageContanst} from '../../Config/Contanst';
import store from '../../redux/store/store';
import {getDataToAsyncStorage} from '../../utils/AsyncStorage';
import {randomGID} from '../../utils/Utils';
import {CustomerDA} from '../customer/da';
import {examDA} from '../exam/da';

export interface CourseItem {
  Id?: string;
  Name?: string;
  Img?: string;
  Price: number;
  IsCare: boolean;
  Description?: string;
  Status?: number;
}

export class CourseDA {
  private courseController: DataController;
  private orderDetailController: DataController;
  private lessonController: DataController;
  private wishlistCourseController: DataController;
  private courseCustomerController: DataController;
  private ratingController: DataController;
  private likeRatingController: DataController;
  private customerLessonController: DataController;
  private certificateController: DataController;

  constructor(
    courseController = new DataController('Course'),
    orderDetailController = new DataController('OrderDetail'),
    lessonController = new DataController('Lesson'),
    wishlistCourseController = new DataController('WishlistCourse'),
    courseCustomerController = new DataController('Course_Customer'),
    ratingController = new DataController('Rating'),
    likeRatingController = new DataController('Like_Rating'),
    customerLessonController = new DataController('CustomerLesson'),
    certificateController = new DataController('Customer_Certificate'),
  ) {
    this.courseController = courseController;
    this.orderDetailController = orderDetailController;
    this.lessonController = lessonController;
    this.wishlistCourseController = wishlistCourseController;
    this.courseCustomerController = courseCustomerController;
    this.ratingController = ratingController;
    this.likeRatingController = likeRatingController;
    this.customerLessonController = customerLessonController;
    this.certificateController = certificateController;
  }

  async getCoursePopular(pageSize: number) {
    const responseCourse = await this.orderDetailController.group({
      searchRaw: '*',
      reducers: 'LOAD * GROUPBY 1 @CourseId REDUCE COUNT 0 AS CountCourse',
    });

    if (responseCourse.code === 200) {
      var lstCourse = responseCourse.data;
      //sắp xếp lại khóa học sau khi đã count trong đơn hàng để lấy khóa đc mua nhiều nhất. và lấy top 10
      if (lstCourse.length > 0) {
        if (pageSize > 0) {
          lstCourse = [...lstCourse]
            .sort(
              (a, b) =>
                parseInt(a.CountCourse, 10) - parseInt(b.CountCourse, 10),
            )
            .slice(0, pageSize);
        } else {
          lstCourse = [...lstCourse].sort(
            (a, b) => parseInt(a.CountCourse, 10) - parseInt(b.CountCourse, 10),
          );
        }

        const respone = await this.courseController.getListSimple({
          page: 1,
          size: 50,
          query: `@Id: {${lstCourse
            .map((item: any) => item.CourseId)
            .join(' | ')}}`,
          returns: ['Id', 'Name', 'Price', 'Img'],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      } else {
        const respone = await this.courseController.getListSimple({
          page: 1,
          size: 20,
          query: '@IsHot: {true}',
          returns: ['Id', 'Name', 'Price', 'Img'],
          sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
        });

        if (respone.code === 200) {
          return respone;
        }
      }
    }
    return null;
  }

  async getCourseRecent() {
    var listId = await getDataToAsyncStorage(StorageContanst.RecentCourse);
    if (listId != null) {
      const lst = listId.split(',');
      const respone = await this.courseController.getListSimple({
        query: `@Id: {${lst.join(' | ')}}`,
        returns: ['Id', 'Name', 'Price'],
      });
      if (respone.code === 200) {
        return respone;
      }
    }
    return null;
  }

  async getAllList(
    page: number,
    size: number,
    query: string,
    returns?: Array<string>,
  ) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: query ?? '*',
      returns: returns,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getAllListbyCategory(page?: any, size?: any, cateId?: string) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: `@CategoryId:{${cateId}}`,
      returns: ['Id', 'Name', 'Description', 'Price', 'Img', 'CustomerId'],
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }
  async getAllListbyCustomerId(page: number, size: number, customerId: string) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: `@CustomerId:{${customerId}}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getAllListbyTopic(page: number, size: number, topicId: string) {
    const respone = await this.courseController.getListSimple({
      page: page,
      size: size,
      query: `@TopicId:{*${topicId}*}`,
      sortby: {BY: 'DateCreated', DIRECTION: 'DESC'},
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  // async getMycourse(status: number, customerId: string) {
  //   // lấy khóa học nằm trong đơn hàng đã hoàn thành
  //   const lst = await this.orderDetailController.getListSimple({
  //     page: 1,
  //     size: 1000,
  //     query: `@Status:[${StatusOrder.success}] @CustomerId: {${customerId}}`,
  //   });

  //   if (lst.code === 200) {
  //     var lstCourse = lst.data;
  //     lstCourse = [...lstCourse].map((item: any) => item.CourseId);

  //     const respone = await this.courseCustomerController.getListSimple({
  //       page: 1,
  //       size: 1000,
  //       query: `@CourseId:{${lstCourse.join(' | ')}}`,
  //     });

  //     if (respone.code === 200) {
  //       return respone;
  //     }
  //   }
  //   return null;
  // }

  async studentsCourse(id: string) {
    const respone = await this.orderDetailController.getPatternList({
      query: `@CourseId: {${id}} @Status: [${StatusOrder.success}]`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });
    if (respone.code === 200) {
      return respone;
    }
    return 0;
  }

  async countStudentCourse(id: string) {
    const respone = await this.orderDetailController.getPatternList({
      query: `@CourseId: {${id}} @Status: [${StatusOrder.success}]`,
      pattern: {
        CustomerId: ['Id', 'Name', 'AvatarUrl'],
      },
    });
    if (respone.code === 200) {
      return respone.data.length;
    }
    return 0;
  }

  async getCourseDetail(courseId: string) {
    const respone = await this.courseController.getById(courseId);
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getLessonbyCourseId(courseId: string) {
    const respone = await this.lessonController.getListSimple({
      page: 1,
      query: `@CourseId: {${courseId}}`,
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
      returns: ['Id', 'Name', 'Hours', 'Introduction', 'Video', 'Document'],
    });
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getLessonInCourseDetail(courseId: string, check?: boolean) {
    const examda = new examDA();
    var query = `@CourseId: {${courseId}}`;
    if (!check) {
      query += `@IsShowDetail:{true}`;
    }
    const respone = await this.lessonController.getListSimple({
      page: 1,
      query: query,
      sortby: {BY: 'Sort', DIRECTION: 'ASC'},
    });
    if (respone.code === 200) {
      const lessonIds = respone.data.map((item: any) => item.Id);
      const proccess = await this.getProccessListLesson(courseId, lessonIds);
      var isCompleted = true;
      const list = await Promise.all(
        respone.data.map(async (item: any, index: number) => {
          const steps: {
            type: string;
            Name: string;
            order: number;
            Icon?: string;
            id?: number;
            PercentCompleted: number;
          }[] = [];
          var order = 0;
          if (item.Introduction) {
            order = order + 1;
            const percent =
              proccess?.find((test: any) => test.currentStep === order)
                ?.PercentCompleted ?? 0;
            isCompleted = percent < 100 ? false : isCompleted;
            steps.push({
              type: 'Intro',
              Name: 'Introduction',
              Icon: 'outline/multimedia/video',
              order: order,
              PercentCompleted: percent,
            });
          }
          if (item.Video) {
            order = order + 1;
            const percent =
              proccess?.find((test: any) => test.currentStep === order)
                ?.PercentCompleted ?? 0;
            isCompleted = percent < 100 ? false : isCompleted;
            steps.push({
              type: 'Video',
              Name: 'Content',
              order: order,
              Icon: 'outline/multimedia/video',
              PercentCompleted: percent,
            });
          }
          // if (item.Document) {
          //   order = order + 1;
          //   const percent =
          //     proccess?.find((test: any) => test.currentStep === order)
          //       ?.PercentCompleted ?? 0;
          //   isCompleted = percent < 100 ? false : isCompleted;
          //   steps.push({
          //     type: 'Document',
          //     Name: 'Document',
          //     Icon: 'outline/files/document',
          //     order: order,
          //     PercentCompleted: percent,
          //   });
          // }
          // const quiz = await examda.getListQuizbyLessonId(item.Id);
          // if (quiz?.data?.length > 0) {
          //   order = order + 1;
          //   const percent =
          //     proccess?.find((test: any) => test.currentStep === order)
          //       ?.PercentCompleted ?? 0;
          //   isCompleted = percent < 100 ? false : isCompleted;
          //   steps.push({
          //     type: 'Quiz',
          //     Name: 'Quiz',
          //     Icon: 'outline/user interface/c-question',
          //     order: order,
          //     id: quiz?.data[0].Id,
          //     PercentCompleted: percent,
          //   });
          // }
          const exam = await examda.getListExambyLessonId(item.Id);
          if (exam?.data?.length > 0) {
            order = order + 1;
            const percent =
              proccess?.find((test: any) => test.currentStep === order)
                ?.PercentCompleted ?? 0;
            isCompleted = percent < 100 ? false : isCompleted;
            steps.push({
              type: 'Exam',
              Name: 'Exam',
              Icon: 'outline/files/document',
              order: order,
              id: exam?.data[0].Id,
              PercentCompleted: percent,
            });
          }
          return {
            Id: item.Id,
            Name: item.Name,
            isCompleted: isCompleted,
            step: index + 1,
            listItem: steps,
          };
        }),
      );

      return list;
    }
    return null;
  }

  async getLessonDetail(lessonId: string) {
    const respone = await this.lessonController.getById(lessonId);
    if (respone.code === 200) {
      return respone;
    }
    return null;
  }

  async getWishlistCourse(page: number, size: number) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const respone = await this.wishlistCourseController.getListSimple({
      query: `@CustomerId: {${cusId}}`,
    });

    if (respone.code === 200) {
      var lstId = [...respone.data].map(item => item.CourseId);

      const courserespone = await this.courseController.getListSimple({
        page: page,
        size: size,
        query: `@Id: {${lstId.join(' | ')}}`,
      });

      if (courserespone.code === 200) {
        return courserespone;
      }
    }
    return null;
  }

  async checkCourseIsWishlishCustomer(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.wishlistCourseController.getListSimple({
        query: `@CustomerId: {${cusId}} @CourseId:{${id}}`,
        size: 1,
        returns: ['Id'],
      });
      if (respone.data?.length > 0) {
        return true;
      }
    }
    return false;
  }

  async deleteWishlistCourse(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const course = await this.wishlistCourseController.getListSimple({
      query: `@CourseId: {${id}} @CustomerId: {${cusId}}`,
    });

    if (course?.data?.length > 0) {
      const respone = await this.wishlistCourseController.delete([
        course.data[0].Id,
      ]);
      if (respone.code === 200) {
        return respone;
      }
    }
    return null;
  }

  async addWishlistCourse(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const data = {
      Id: randomGID(),
      CustomerId: cusId,
      CourseId: id,
      DateCreated: new Date().getTime(),
    };

    const respone = await this.wishlistCourseController.add([data]);
    if (respone.code === 200) {
      return data;
    }
    return null;
  }

  async getRatingCourse(id: string) {
    const courserespone = await this.ratingController.getListSimple({
      query: `@CourseId: {${id}}`,
    });
    if (courserespone.code === 200) {
      return courserespone;
    }
    return null;
  }

  async getLikesRatingCourse(id: string) {
    const courserespone = await this.likeRatingController.getListSimple({
      query: `@RatingId: {${id}}`,
    });
    if (courserespone.code === 200) {
      return courserespone.data?.length ?? 0;
    }
    return 0;
  }

  async addRating(data: any) {
    const courserespone = await this.ratingController.add([data]);
    if (courserespone.code === 200) {
      return data;
    }
    return null;
  }

  async addLikeRating(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const data = {
      Id: randomGID(),
      RatingId: id,
      CustomerId: cusId,
    };
    const courserespone = await this.likeRatingController.add([data]);
    if (courserespone.code === 200) {
      return data;
    }
    return false;
  }

  async addCommentRating(id: string, content: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    const data = {
      Id: randomGID(),
      RatingId: id,
      CustomerId: cusId,
      Message: content,
      DateCreated: new Date().getTime(),
    };
    const courserespone = await this.ratingController.add([data]);
    if (courserespone.code === 200) {
      return data;
    }
    return null;
  }
  async getMyCourse() {
    var cusId = store.getState().customer.data?.Id;
    const customerDa = new CustomerDA();
    if (cusId) {
      const respone = await this.courseCustomerController.getPatternList({
        query: `@CustomerId: {${cusId}} @Status: [${StatusOrder.success}]`,
        pattern: {
          CourseId: ['Id', 'Name', 'CustomerId', 'Img'],
        },
      });
      if (respone.code === 200) {
        const customerIds = respone.Course?.map(
          (a: any) => a?.CustomerId,
        ).filter((item: any, index: any, self: any) => {
          return self.indexOf(item) === index;
        });
        const customers = await customerDa.getCustomersByIds(customerIds); // Gọi API lấy 2 field
        const list = await Promise.all(
          respone.data.map(async (item: any) => {
            const course = respone.Course.find(
              (a: any) => a.Id === item?.CourseId,
            );
            if (course) {
              return {
                ...item,
                Img: course?.Img,
                Name: course?.Name,
                Author: customers?.data?.find(
                  (a: any) => a.Id === course.CustomerId,
                )?.Name,
                Signature: customers?.data?.find(
                  (a: any) => a.Id === course.CustomerId,
                )?.Signature,
                Description: `${item.Process ?? 0}/${
                  item.TotalLesson ?? 0
                } Lesson`,
              };
            } else {
              return {
                ...item,
              };
            }
          }),
        );
        return list;
      }
    }
    return null;
  }
  async getProccessLesson(courseId: string, lessonId: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @LessonId: {${lessonId}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data;
      }
    }
    return null;
  }
  async getProccessVideoLesson(courseId: string, lessonId: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @LessonId: {${lessonId}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data;
      }
    }
    return null;
  }
  async getProccessListLesson(courseId: string, lessonId: Array<string>) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @LessonId: {${lessonId.join(
          ' | ',
        )}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data;
      }
    }
    return null;
  }
  async checkExistsProccessLesson(courseId: string, lessonId: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.getListSimple({
        query: `@CourseId: {${courseId}} @LessonId: {${lessonId}} @CustomerId:{${cusId}}`,
      });
      if (respone.code === 200 && respone?.data?.length > 0) {
        return respone.data[0];
      }
    }
    return null;
  }

  async addProccessLesson(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.add([data]);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  async updateProccessLesson(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.customerLessonController.edit([data]);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  async updateCourseCustomer(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.courseCustomerController.edit([data]);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  async getCourseCustomerItem(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.courseCustomerController.getById(id);
      if (respone?.code === 200) {
        return respone.data;
      }
    }
    return null;
  }
  //certi
  async getListCertificate() {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.getListSimple({
        query: `@CustomerId: {${cusId}}`,
      });
      if (respone?.code === 200) {
        return respone;
      }
    }
    return null;
  }
  async getCertificateItembyId(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.getById(id);
      if (respone?.code === 200) {
        return respone;
      }
    }
    return null;
  }
  async getCertificateItem(id: string) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.getListSimple({
        query: `@CustomerId: {${cusId}} @CourseId: {${id}}`,
      });
      if (respone?.code === 200 && respone?.data?.length > 0) {
        return respone?.data[0];
      }
    }
    return null;
  }
  async addCertificate(data: any) {
    var cusId = await getDataToAsyncStorage(StorageContanst.CustomerId);
    if (cusId) {
      const respone = await this.certificateController.add([data]);
      if (respone?.code === 200) {
        return respone;
      }
    }
    return null;
  }
  //end certi
}
